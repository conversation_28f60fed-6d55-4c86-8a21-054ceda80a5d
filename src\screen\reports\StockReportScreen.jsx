import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';

import { bearerToken, loginBranchID } from '../../globals';
import { fetchStockReport } from '../../apiHandling/ReportAPI/fetchStockReportAPI';

const StockReportScreen = () => {
  // UI control states
  const [selectedScrollOption, setSelectedScrollOption] = useState('');

  const [stockData, setStockData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStockData = async () => {
      setIsLoading(true);
      const data = await fetchStockReport(bearerToken, loginBranchID); // pass token + branchId
      setStockData(data);
      setIsLoading(false);
    };

    loadStockData();
  }, []);

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Stock Report</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              const isSelected = selectedScrollOption === option;
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02720F' : '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                    onPress={() => setSelectedScrollOption(option)}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: isSelected ? 'white' : 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.stockReportContainer}>
          {/* Stock Report Table */}
          <View style={styles.tableContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={true}>
              <View style={styles.tableWrapper}>
                <DataTable>
                  <DataTable.Header style={styles.tableHeader}>
                    <DataTable.Title style={styles.columnItemId}>
                      <Text style={styles.tableHeaderText}>Item ID</Text>
                    </DataTable.Title>
                    <DataTable.Title style={styles.columnItemName}>
                      <Text style={styles.tableHeaderText}>Item Name</Text>
                    </DataTable.Title>
                    <DataTable.Title style={styles.columnCategoryName}>
                      <Text style={styles.tableHeaderText}>Category Name</Text>
                    </DataTable.Title>
                    <DataTable.Title style={styles.columnStockQty}>
                      <Text style={styles.tableHeaderText}>Stock Qty</Text>
                    </DataTable.Title>
                    <DataTable.Title style={styles.columnStockAltQty}>
                      <Text style={styles.tableHeaderText}>Stock Alt Qty</Text>
                    </DataTable.Title>
                  </DataTable.Header>

                  {isLoading ? (
                    <DataTable.Row>
                      <DataTable.Cell style={{ flex: 1 }}>
                        <Text style={styles.cellText}>Loading stock...</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  ) : stockData.length === 0 ? (
                    <DataTable.Row>
                      <DataTable.Cell style={{ flex: 1 }}>
                        <Text style={styles.cellText}>No stock data found</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  ) : (
                    stockData.map((item, index) => (
                      <DataTable.Row key={index}>
                        <DataTable.Cell style={styles.columnItemId}>
                          <Text style={styles.cellText}>{item.ItemID}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnItemName}>
                          <Text style={styles.cellText}>{item.ItemName}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnCategoryName}>
                          <Text style={styles.cellText}>{item.CategoryName}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnStockQty}>
                          <Text style={styles.cellText}>{item.StockQty}</Text>
                        </DataTable.Cell>
                        <DataTable.Cell style={styles.columnStockAltQty}>
                          <Text style={styles.cellText}>{item.StockAltQty}</Text>
                        </DataTable.Cell>
                      </DataTable.Row>
                    ))
                  )}
                </DataTable>
              </View>
            </ScrollView>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Stock Report Container Styles
  stockReportContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Table Styles
  tableContainer: {
    marginVertical: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  tableWrapper: {
    minWidth: '100%',
  },
  tableHeader: {
    backgroundColor: '#02096A',
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  cellText: {
    fontSize: 14,
    color: '#333',
  },
  // Dynamic column widths for Stock Report table
  columnItemId: {
    minWidth: 120,
    maxWidth: 150,
    flex: 0,
  },
  columnItemName: {
    minWidth: 200,
    maxWidth: 300,
    flex: 1,
  },
  columnCategoryName: {
    minWidth: 150,
    maxWidth: 200,
    flex: 1,
  },
  columnStockQty: {
    minWidth: 100,
    maxWidth: 120,
    flex: 0,
  },
  columnStockAltQty: {
    minWidth: 120,
    maxWidth: 150,
    flex: 0,
  },
});

export default StockReportScreen;