import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import Navbar from '../../components/Navbar';
import CheckBox from '@react-native-community/checkbox';

const vehicleTypes = [
  { label: 'Truck', value: 'truck' },
  { label: 'Van', value: 'van' },
  { label: 'Bike', value: 'bike' },
];

const ports = [
  { label: 'COM 1', value: 'COM 1' },
  { label: 'COM 2', value: 'COM 2' },
  { label: 'COM 3', value: 'COM 3' },
];

const addresses = [
  { label: 'Warehouse A', value: 'warehouse_a' },
  { label: 'Warehouse B', value: 'warehouse_b' },
  { label: 'Branch C', value: 'branch_c' },
];

const TransferOut = ({ navigation }) => {
  const [vehicleType, setVehicleType] = useState(null);
  const [address, setAddress] = useState(null);
  const [port, setPort] = useState(null);
  const [kgs, setKgs] = useState('');
  const [nos, setNos] = useState('');
  const [itemName, setItemName] = useState('');
  const [remarks, setRemarks] = useState('');
  const [items, setItems] = useState([]);
  const [batch, setBatch] = useState('');
  const [stockQty, setStockQty] = useState('');
  const [rate, setRate] = useState('');
  const [editingItemId, setEditingItemId] = useState(null);
  const [isEditing, setIsEditing] = useState(false);


  const totalWeight = '50'; // or calculated value
  const totalProductValue = '₹1000';
  const totalTax = '₹180';
  const totalAmount = '₹1180';


  const handleAddItem = () => {
    if (!itemName || !nos || !kgs) return;

    if (isEditing && editingItemId) {
      // Update existing item
      setItems(items.map(item =>
        item.id === editingItemId
          ? {
            ...item,
            itemName,
            batch,
            port,
            stockQty,
            nos,
            rate,
            kgs,
            remarks
          }
          : item
      ));
      setIsEditing(false);
      setEditingItemId(null);
    } else {
      // Add new item
      const newItem = {
        id: Date.now().toString(),
        itemName,
        batch,
        port,
        stockQty,
        nos,
        rate,
        kgs,
        remarks,
        selected: false,
      };
      setItems([...items, newItem]);
    }

    // Clear form fields
    handleClear();
  };

  const handleEditItem = (item) => {
    setItemName(item.itemName);
    setBatch(item.batch);
    setPort(item.port);
    setStockQty(item.stockQty);
    setNos(item.nos);
    setRate(item.rate);
    setKgs(item.kgs);
    setRemarks(item.remarks);
    setEditingItemId(item.id);
    setIsEditing(true);
  };

  const handleClear = () => {
    setItemName('');
    setBatch('');
    setPort(null);
    setStockQty('');
    setNos('');
    setRate('');
    setKgs('');
    setRemarks('');
    setIsEditing(false);
    setEditingItemId(null);
  };

  const toggleItemSelection = (id) => {
    setItems(items.map(item =>
      item.id === id ? { ...item, selected: !item.selected } : item
    ));
  };

  const deleteSelectedItems = () => {
    setItems(items.filter(item => !item.selected));
  };

  return (
    <View style={styles.container}>
      <Navbar />
      <ScrollView contentContainerStyle={styles.scrollContent}>

        {/* Header and other sections remain the same... */}
        {/* Header */}
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-left" size={20} color="#000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>TRANSFER OUT</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.newBtn}>
              <Icon name="user-plus" size={16} color="#000" />
              <Text style={styles.actionText}> New</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.viewBtn}>
              <Icon name="eye" size={16} color="#000" />
              <Text style={styles.actionText}> View</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveBtn}>
              <Text style={styles.saveText}>Save</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cancelBtn}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Indent Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Indent Details</Text>
          <View style={styles.inputRow}>
            <View style={styles.inputWithButton}>
              <TextInput placeholder="Transfer Order No." style={styles.inputBox} />
              <TouchableOpacity style={styles.selectBtn}>
                <Text style={styles.btnTextWhite}>Select</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.inputWithButton}>
              <TextInput placeholder="Indent No." style={styles.inputBox} />
              <TouchableOpacity style={styles.selectBtn}>
                <Text style={styles.btnTextWhite}>Select</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Location & Vehicle Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location & Vehicle Details</Text>
          <View style={styles.inputRow}>
            <TextInput placeholder="Choose Location" style={styles.inputBox} />
            <TouchableOpacity style={styles.selectBtn}>
              <Text style={styles.btnTextWhite}>Select</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.transitBtn}>
              <Text style={styles.btnTextWhite}>Transit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputRow}>
            <View style={styles.dropdownWrapper}>
              <Dropdown
                style={styles.dropdown}
                data={vehicleTypes}
                labelField="label"
                valueField="value"
                placeholder="Select Vehicle Type"
                value={vehicleType}
                onChange={item => setVehicleType(item.value)}
                selectedTextStyle={styles.selectedTextStyle}
                placeholderStyle={styles.placeholderStyle}
              />
            </View>

            <View style={styles.dropdownWrapper}>
              <Dropdown
                style={styles.dropdown}
                data={addresses}
                labelField="label"
                valueField="value"
                placeholder="Select Address"
                value={address}
                onChange={item => setAddress(item.value)}
                selectedTextStyle={styles.selectedTextStyle}
                placeholderStyle={styles.placeholderStyle}
              />
            </View>

            <TouchableOpacity style={styles.newBtnSmall}>
              <Text style={styles.btnTextWhite}>New</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputRow}>
            <TextInput placeholder="Distance" style={styles.dropdownBox} />
            <TextInput placeholder="Vehicle Number" style={styles.dropdownBox} />
            <TextInput placeholder="Driver" style={styles.dropdownBox} />
          </View>

          <View style={styles.inputRow}>
            <TextInput placeholder="Add Remarks" style={styles.remarkInputlocation} />
            <TouchableOpacity style={styles.LocationclearBtn}>
              <Text style={styles.btnTextWhite}>Clear</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Item Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Item Details</Text>

          <View style={styles.inputRow}>
            <TextInput
              placeholder="Item Name"
              style={styles.inputBox}
              value={itemName}
              onChangeText={setItemName}
            />
            <TouchableOpacity style={styles.selectBtn}>
              <Text style={styles.btnTextWhite}>Select</Text>
            </TouchableOpacity>

            <TextInput
              placeholder="Choose Batch"
              style={styles.inputBox}
              value={batch}
              onChangeText={setBatch}
            />
            <TouchableOpacity style={styles.selectBtn}>
              <Text style={styles.btnTextWhite}>Select</Text>
            </TouchableOpacity>

            <View style={styles.dropdownWrapperPort}>
              <Dropdown
                style={styles.dropdownPort}
                data={ports}
                labelField="label"
                valueField="value"
                placeholder="Select Port"
                value={port}
                onChange={item => setPort(item.value)}
                selectedTextStyle={styles.selectedTextStyle}
                placeholderStyle={styles.placeholderStyle}
              />
            </View>
          </View>

          <View style={styles.inputRow}>
            <TextInput
              placeholder="Stock Qty"
              style={styles.inputSmall}
              keyboardType="numeric"
              value={stockQty}
              onChangeText={setStockQty}
            />
            <TextInput
              placeholder="Nos"
              style={styles.inputSmall}
              keyboardType="numeric"
              value={nos}
              onChangeText={setNos}
            />
            <TextInput
              placeholder="Rate"
              style={styles.inputSmall}
              keyboardType="numeric"
              value={rate}
              onChangeText={setRate}
            />
            <TextInput
              placeholder="Wt(Kg)"
              style={styles.inputSmall}
              keyboardType="numeric"
              value={kgs}
              onChangeText={setKgs}
            />
          </View>

          <View style={styles.row}>
            <TextInput
              placeholder="Remarks"
              style={styles.remarkInput}
              value={remarks}
              onChangeText={setRemarks}
            />
            <TouchableOpacity
              style={isEditing ? styles.updateBtn : styles.addBtn}
              onPress={handleAddItem}
            >
              <Text style={styles.btnText}>{isEditing ? 'Update' : 'Add'}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.clearBtn} onPress={handleClear}>
              <Text style={styles.btnText}>Clear</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Table Section */}
        <View style={styles.section}>
          <View style={styles.tableContainer}>
            <View style={styles.tableHeader}>
              <Text style={[styles.headerCell, styles.checkboxCell]}>Select</Text>
              <Text style={[styles.headerCell, styles.lineNumberCell]}>Line No.</Text>
              <Text style={[styles.headerCell, styles.itemNameCell]}>Item Name</Text>
              <Text style={[styles.headerCell, styles.nosCell]}>Nos</Text>
              <Text style={[styles.headerCell, styles.kgsCell]}>Kgs</Text>
              <Text style={[styles.headerCell, styles.remarksCell]}>Remarks</Text>
              <Text style={[styles.headerCell, styles.actionCell]}>Action</Text>
            </View>

            <View style={styles.tableScrollContainer}>
              <ScrollView>
                {items.length === 0 ? (
                  <View style={[styles.tableRow, { justifyContent: 'center' }]}>
                    <Text style={{ color: '#999', paddingVertical: 20 }}>No items added yet</Text>
                  </View>
                ) : (
                  items.map((item, index) => (
                    <View key={item.id} style={[styles.tableRow, index % 2 === 0 && styles.tableRowEven]}>
                      <View style={[styles.cell, styles.checkboxCell]}>
                        <CheckBox
                          value={item.selected}
                          onValueChange={() => toggleItemSelection(item.id)}
                          tintColors={{ true: '#002b5c', false: '#002b5c' }}
                          style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }} // Adjust scale values as needed
                        />
                      </View>
                      <Text style={[styles.cell, styles.lineNumberCell]}>{index + 1}</Text>
                      <Text style={[styles.cell, styles.itemNameCell]}>{item.itemName}</Text>
                      <Text style={[styles.cell, styles.nosCell]}>{item.nos}</Text>
                      <Text style={[styles.cell, styles.kgsCell]}>{item.kgs}</Text>
                      <Text style={[styles.cell, styles.remarksCell]}>{item.remarks}</Text>
                      <View style={[styles.cell, styles.actionCell]}>
                        <TouchableOpacity onPress={() => handleEditItem(item)}>
                          <Icon name="edit" size={30} color="green" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))
                )}
              </ScrollView>
            </View>

            {/* Table Footer */}
       
            <View style={styles.footerRow}>
              {/* Delete Button */}
              <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
                <Text style={styles.footerText}>Delete</Text>
              </TouchableOpacity>

              {/* Wt(kg) */}
              <View style={styles.footerBox}>
                <Text style={styles.footerLabel}>Wt(kg): </Text>
                <Text style={styles.footerValue}>{totalWeight}</Text>
              </View>

              {/* Prod Value */}
              <View style={styles.footerBox}>
                <Text style={styles.footerLabel}>Prod Value: </Text>
                <Text style={styles.footerValue}>{totalProductValue}</Text>
              </View>

              {/* Tax */}
              <View style={styles.footerBox}>
                <Text style={styles.footerLabel}>Tax: </Text>
                <Text style={styles.footerValue}>{totalTax}</Text>
              </View>

              {/* Total Amt */}
              <View style={[styles.footerBox, styles.footerBoxHighlight]}>
                <Text style={[styles.footerLabel, styles.footerLabelHighlight]}>Total Amt: </Text>
                <Text style={[styles.footerValue, styles.footerLabelHighlight]}>{totalAmount}</Text>
              </View>
            </View>

          </View>
        </View>

      </ScrollView>
    </View>
  );
};


const styles = StyleSheet.create({

  container: {
    flex: 1,
    backgroundColor: '#F7F8F9'

  },
  scrollContent: {
    paddingBottom: 20,
  },

  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    backgroundColor: '#e9e9e9',
    padding: 20,
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginLeft: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 5,
    flexWrap: 'wrap',
  },

  newBtn: {
    backgroundColor: '#E2E3E5',
    paddingVertical: 14,
    paddingHorizontal: 25,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
  },
  viewBtn: {
    backgroundColor: '#E2E3E5',
    paddingVertical: 14,
    paddingHorizontal: 25,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
  },
  saveBtn: {
    backgroundColor: '#28A745',
    paddingVertical: 14,
    paddingHorizontal: 25,
    borderRadius: 8,
  },

  cancelBtn: {
    backgroundColor: 'red',
    paddingVertical: 14,
    paddingHorizontal: 25,
    borderRadius: 8,
  },

  actionText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  saveText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },


  section: {
    backgroundColor: '#e9e9e9',
    paddingHorizontal: 10,
    marginBottom: 0,

  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    marginTop: 0, // ↓ Ensures no extra space on top
    color: '#000',
  },

  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
    width: '100%',
  },

  inputBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#999',
    borderRadius: 6,
    paddingHorizontal: 10,
    height: 50, // Increased from 40
    backgroundColor: '#fff',
  },

  dropdownBox: {
    flex: 1,
    minWidth: '30%',
    borderWidth: 1,
    borderColor: '#aaa',
    borderRadius: 6,
    paddingHorizontal: 10,
    height: 50, // Increased from 40
    backgroundColor: '#fff',
  },

  dropdownWrapper: {
    flex: 1,
    minWidth: '30%',
  },

  dropdownWrapperPort: {
    width: 120,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    backgroundColor: '#fff',
  },


  dropdown: {
    height: 50, // Increased from 40
    borderColor: '#aaa',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
  },
  dropdownPort: {
    height: 50, // Increased from 40
    paddingHorizontal: 8,
  },
  selectedTextStyle: {
    fontSize: 14,
  },
  placeholderStyle: {
    fontSize: 14,
    color: '#888',
  },
  selectBtn: {
    paddingHorizontal: 12,
    paddingVertical: 12, // Increased
    backgroundColor: '#1E2D52',
    borderRadius: 6,
    height: 50, // Increased from 40
    justifyContent: 'center',
    alignItems: 'center',
  },
  transitBtn: {
    backgroundColor: '#1E2D52',
    paddingHorizontal: 12,
    paddingVertical: 12, // Increased
    borderRadius: 6,
    height: 50, // Increased from 40
    justifyContent: 'center',
    alignItems: 'center',
  },
  newBtnSmall: {
    backgroundColor: '#28A745',
    height: 50, // Increased from 40
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 6,
    width: 70,
  },

  remarkInput: {
    height: 50,
    backgroundColor: '#fff',
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginRight: 8,
  },

  remarkInputlocation: {
    height: 50,
    backgroundColor: '#fff',
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
  },


  addBtn: {
    backgroundColor: 'green',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
    marginRight: 10,             // gap between Add and Clear
  },

  clearBtn: {
    backgroundColor: 'red',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
  },

  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  LocationclearBtn: {
    backgroundColor: 'red',
    paddingHorizontal: 12,
    paddingVertical: 12, // Increased
    height: 50, // Increased from 40
    borderRadius: 6,
    width: 70,
    justifyContent: 'center',
    alignItems: 'center',
  },

  inputSmall: {
    flex: 1,
    minWidth: '22%',
    height: 50, // Increased from 40
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 14, // Added for better visibility
  },
  btnTextWhite: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14, // Added for better visibility
  },
  inputWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    minWidth: '48%',
    gap: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },


  tableScrollContainer: {
    maxHeight: 240, // Adjust based on your row height. ~50px * 4 rows
    borderWidth: 1,
    borderColor: '#ccc',

  },

  tableContainer: {
    marginTop: 10,
    overflow: 'hidden',
    borderRadius: 5,
    marginBottom: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#002b5c',
    paddingVertical: 14,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  headerCell: {
    flex: 1,
    fontWeight: '600',
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#f0f0f0',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  tableRowEven: {
    backgroundColor: '#f9f9f9',
  },
  cell: {
    flex: 1,
    paddingHorizontal: 8,
    textAlign: 'center',
    fontSize: 13,
    color: '#333',
  },
  checkboxCell: {
    flex: 0.4,
    textAlign: 'center',
  },
  lineNumberCell: {
    flex: 0.7,
  },
  itemNameCell: {
    flex: 1.5,
    textAlign: 'left',
  },
  nosCell: {
    flex: 0.8,
  },
  kgsCell: {
    flex: 0.8,
  },
  remarksCell: {
    flex: 1.2,
    textAlign: 'left',
  },


  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#fff',
    borderColor: '#ccc',

    flexWrap: 'wrap',
  },

  footerBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#dcdcdc',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 6,
    height: 40,
    marginLeft: 8,
  },

  footerBoxHighlight: {
    backgroundColor: '#bcd4ff',
  },

  footerLabel: {
    color: '#333',
    fontWeight: '600',
  },

  footerLabelHighlight: {
    color: '#003f8a',
  },

  footerValue: {
    color: '#000',
    fontWeight: '600',
    marginLeft: 4,
  },

  deleteBtn: {
    backgroundColor: 'red',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 6,
  },

  footerText: {
    color: '#fff',
    fontWeight: 'bold',
  },

  updateBtn: {
    backgroundColor: '#ffa500',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
    marginRight: 10,
  },
  actionCell: {
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },


});

export default TransferOut;