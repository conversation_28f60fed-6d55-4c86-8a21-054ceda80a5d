import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Text, StyleSheet } from 'react-native';

const AddAddressScreen = ({ navigation }) => {
  const [flatNo, setFlatNo] = useState('');
  const [building, setBuilding] = useState('');
  const [address, setAddress] = useState('');
  const [landmark, setLandmark] = useState('');
  const [pincode, setPincode] = useState('');
  const [area, setArea] = useState('');
  const [state, setState] = useState('');
  const [city, setCity] = useState('');

  const handleSave = () => {
    const newAddress = {
      flatNo,
      building,
      address,
      landmark,
      pincode,
      area,
      state,
      city,
    };
    navigation.navigate('CustomerDetails', { newAddress });
  };

  return (
    <View style={styles.container}>
      <TextInput placeholder="Flat No" value={flatNo} onChangeText={setFlatNo} style={styles.input} />
      <TextInput placeholder="Building" value={building} onChangeText={setBuilding} style={styles.input} />
      <TextInput placeholder="Address" value={address} onChangeText={setAddress} style={styles.input} />
      <TextInput placeholder="Landmark" value={landmark} onChangeText={setLandmark} style={styles.input} />
      <TextInput placeholder="PINCODE" value={pincode} onChangeText={setPincode} style={styles.input} />
      <TextInput placeholder="Area" value={area} onChangeText={setArea} style={styles.input} />
      <TextInput placeholder="State" value={state} onChangeText={setState} style={styles.input} />
      <TextInput placeholder="City" value={city} onChangeText={setCity} style={styles.input} />

      <TouchableOpacity onPress={handleSave} style={styles.button}>
        <Text style={styles.buttonText}>Save Address</Text>
      </TouchableOpacity>
    </View>
  );
};

export default AddAddressScreen;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  input: {
    borderWidth: 1,
    padding: 10,
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#006400',
    padding: 12,
    marginTop: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
  },
});
