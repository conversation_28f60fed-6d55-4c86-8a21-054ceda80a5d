import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import Navbar from '../../components/Navbar';
import CheckBox from '@react-native-community/checkbox';

const GenMaterialIndent = ({ navigation }) => {
  const [location, setLocation] = useState('');
  const [itemName, setItemName] = useState('');
  const [qty, setQty] = useState('');
  const [uom, setUom] = useState('');
  const [remarks, setRemarks] = useState('');

  const [items, setItems] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItemId, setEditingItemId] = useState(null);

  const handleAddItem = () => {
    if (!location || !itemName || !qty || !uom) return;

    const newItem = {
      id: isEditing ? editingItemId : Date.now().toString(),
      location,
      itemName,
      qty,
      uom,
      remarks,
      selected: false,
    };

    if (isEditing) {
      setItems(items.map(item => item.id === editingItemId ? newItem : item));
    } else {
      setItems([...items, newItem]);
    }

    handleClear();
  };

  const handleEditItem = (item) => {
    setLocation(item.location);
    setItemName(item.itemName);
    setQty(item.qty);
    setUom(item.uom);
    setRemarks(item.remarks);
    setEditingItemId(item.id);
    setIsEditing(true);
  };

  const handleClear = () => {
    setLocation('');
    setItemName('');
    setQty('');
    setUom('');
    setRemarks('');
    setIsEditing(false);
    setEditingItemId(null);
  };

  const toggleItemSelection = (id) => {
    setItems(items.map(item =>
      item.id === id ? { ...item, selected: !item.selected } : item
    ));
  };

  const deleteSelectedItems = () => {
    setItems(items.filter(item => !item.selected));
  };

  return (
    <View style={styles.container}>
      <Navbar />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-left" size={20} color="#000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}> GENERAL MATERIAL INDENT</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.newBtn}>
              <Icon name="user-plus" size={16} color="#000" />
              <Text style={styles.actionText}> New</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.viewBtn}>
              <Icon name="eye" size={16} color="#000" />
              <Text style={styles.actionText}> View</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveBtn}>
              <Text style={styles.saveText}>Save</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cancelBtn}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Item Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Item Details</Text>

          <View style={styles.inputRow}>
            <TextInput
              placeholder="Location"
              style={styles.inputBox}
              value={location}
              onChangeText={setLocation}
            />
             <TouchableOpacity style={styles.selectBtn}>
                          <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>
            <TextInput
              placeholder="Item Name"
              style={styles.inputBox}
              value={itemName}
              onChangeText={setItemName}
            />
              <TouchableOpacity style={styles.selectBtn}>
                          <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>
          </View>

          <View style={styles.inputRow}>
            <TextInput
              placeholder="Qty"
              style={styles.inputSmall}
              keyboardType="numeric"
              value={qty}
              onChangeText={setQty}
            />
            <TextInput
              placeholder="UOM"
              style={styles.inputSmall}
              value={uom}
              onChangeText={setUom}
            />
          </View>

          <View style={styles.row}>
            <TextInput
              placeholder="Remarks"
              style={styles.remarkInput}
              value={remarks}
              onChangeText={setRemarks}
            />
            <TouchableOpacity
              style={isEditing ? styles.updateBtn : styles.addBtn}
              onPress={handleAddItem}
            >
              <Text style={styles.btnText}>{isEditing ? 'Update' : 'Add'}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.clearBtn} onPress={handleClear}>
              <Text style={styles.btnText}>Clear</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Table Section */}
        <View style={styles.section}>
          <View style={styles.tableContainer}>
            <View style={styles.tableHeader}>
              <Text style={[styles.headerCell, styles.checkboxCell]}>Select</Text>
              <Text style={[styles.headerCell, styles.lineNumberCell]}>Line No</Text>
              <Text style={[styles.headerCell, styles.itemNameCell]}>Item Name</Text>
              <Text style={[styles.headerCell, styles.nosCell]}>Qty</Text>
              <Text style={[styles.headerCell, styles.kgsCell]}>UOM</Text>
              <Text style={[styles.headerCell, styles.remarksCell]}>Remarks</Text>
              <Text style={[styles.headerCell, styles.actionCell]}>Action</Text>
            </View>
              <View style={styles.tableScrollContainer}>
            <ScrollView>
              {items.length === 0 ? (
                <View style={[styles.tableRow, { justifyContent: 'center' }]}>
                  <Text style={{ color: '#999', paddingVertical: 20 }}>No items added yet</Text>
                </View>
              ) : (
                items.map((item, index) => (
                  <View key={item.id} style={[styles.tableRow, index % 2 === 0 && styles.tableRowEven]}>
                    <View style={[styles.cell, styles.checkboxCell]}>
                      <CheckBox
                        value={item.selected}
                        onValueChange={() => toggleItemSelection(item.id)}
                        tintColors={{ true: '#002b5c', false: '#002b5c' }}
                        style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                      />
                    </View>
                    <Text style={[styles.cell, styles.lineNumberCell]}>{index + 1}</Text>
                    <Text style={[styles.cell, styles.itemNameCell]}>{item.itemName}</Text>
                    <Text style={[styles.cell, styles.nosCell]}>{item.qty}</Text>
                    <Text style={[styles.cell, styles.kgsCell]}>{item.uom}</Text>
                    <Text style={[styles.cell, styles.remarksCell]}>{item.remarks}</Text>
                    <View style={[styles.cell, styles.actionCell]}>
                      <TouchableOpacity onPress={() => handleEditItem(item)}>
                        <Icon name="edit" size={30} color="green" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))
              )}
            </ScrollView>
              </View>
            {/* Table Footer */}
            <View style={styles.footerRow}>
              <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
                <Text style={styles.footerText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e9e9e9',

  },
  scrollContent: {
    paddingBottom: 20,
  },

  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    backgroundColor: '#e9e9e9',
    padding: 20,
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginLeft: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 5,
    flexWrap: 'wrap',
  },

  newBtn: {
    backgroundColor: '#E2E3E5',
    paddingVertical: 14,
    paddingHorizontal: 25,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
  },
  viewBtn: {
    backgroundColor: '#E2E3E5',
    paddingVertical: 14,
    paddingHorizontal: 25,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
  },
  saveBtn: {
    backgroundColor: '#28A745',
    paddingVertical: 14,
    paddingHorizontal: 25,
    borderRadius: 8,
  },

  cancelBtn: {
    backgroundColor: 'red',
    paddingVertical: 14,
    paddingHorizontal: 25,
    borderRadius: 8,
  },

  actionText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  saveText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },


  section: {
    backgroundColor: '#e9e9e9',
    paddingHorizontal: 10,
    marginTop:10,
    marginBottom: 0,

  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    marginTop: 0, // ↓ Ensures no extra space on top
    color: '#000',
  },

  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
    width: '100%',
  },

  inputBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#999',
    borderRadius: 6,
    paddingHorizontal: 10,
    height: 50, // Increased from 40
    backgroundColor: '#fff',
  },

 
 




 

  selectBtn: {
    paddingHorizontal: 12,
    paddingVertical: 12, // Increased
    backgroundColor: '#1E2D52',
    borderRadius: 6,
    height: 50, // Increased from 40
    justifyContent: 'center',
    alignItems: 'center',
  },



  remarkInput: {
    height: 50,
    backgroundColor: '#fff',
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginRight: 8,
  },



  addBtn: {
    backgroundColor: 'green',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
    marginRight: 10,             // gap between Add and Clear
  },

  clearBtn: {
    backgroundColor: 'red',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
  },

  inputSmall: {
    flex: 1,
    minWidth: '22%',
    height: 50, // Increased from 40
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 14, // Added for better visibility
  },
  btnTextWhite: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14, // Added for better visibility
  },

  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    
  },


tableScrollContainer: {
  maxHeight: 500, // 50px * 10 rows
  borderWidth: 1,
  borderColor: '#ccc',
},


  tableContainer: {
    marginTop: 10,
    overflow: 'hidden',
    borderRadius: 5,
    marginBottom: 10,
    
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#002b5c',
    paddingVertical: 14,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  headerCell: {
    flex: 1,
    fontWeight: '600',
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#f0f0f0',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  tableRowEven: {
    backgroundColor: '#f9f9f9',
  },
  cell: {
    flex: 1,
    paddingHorizontal: 8,
    textAlign: 'center',
    fontSize: 13,
    color: '#333',
  },
  checkboxCell: {
    flex: 0.4,
    textAlign: 'center',
  },
  lineNumberCell: {
    flex: 0.9,
  },
  itemNameCell: {
    flex: 1.7,
    textAlign: 'left',
  },
  nosCell: {
    flex: 0.8,
  },
  kgsCell: {
    flex: 0.8,
  },
  remarksCell: {
    flex: 1.5,
    textAlign: 'left',
  },


  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#fff',
    borderColor: '#ccc',
    flexWrap: 'wrap',
  },

  footerBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#dcdcdc',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 6,
    height: 40,
    marginLeft: 8,
  },

  footerBoxHighlight: {
    backgroundColor: '#bcd4ff',
  },

  footerLabel: {
    color: '#333',
    fontWeight: '600',
  },

  footerLabelHighlight: {
    color: '#003f8a',
  },

  footerValue: {
    color: '#000',
    fontWeight: '600',
    marginLeft: 4,
  },

  deleteBtn: {
    backgroundColor: 'red',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
    marginRight: 10,
  },

  footerText: {
    color: '#fff',
    fontWeight: 'bold',
  },

  updateBtn: {
    backgroundColor: '#ffa500',
    width: 90,
    height: 50,
    justifyContent: 'center',    // center vertically
    alignItems: 'center',        // center horizontally
    borderRadius: 5,
    marginRight: 10,
  },
  actionCell: {
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
     flex: 0.8,
  },


});

export default GenMaterialIndent;
