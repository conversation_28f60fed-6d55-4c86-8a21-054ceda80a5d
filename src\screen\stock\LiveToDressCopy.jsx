import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, FlatList, StyleSheet } from 'react-native';
import { Picker } from '@react-native-picker/picker';

const LiveToDress = () => {
  const [conversionType, setConversionType] = useState('');
  const [liveItem, setLiveItem] = useState('');
  const [port, setPort] = useState('');
  const [dressItem, setDressItem] = useState('');
  const [liveItemsList, setLiveItemsList] = useState([]);
  const [dressItemsList, setDressItemsList] = useState([]);

  const renderItemRow = ({ item }) => (
    <View style={styles.tableRow}>
      <Text style={styles.cell}>{item.lineNumber}</Text>
      <Text style={styles.cell}>{item.name}</Text>
      <Text style={styles.cell}>{item.nos}</Text>
      <Text style={styles.cell}>{item.kgs}</Text>
      <Text style={styles.cell}>{item.remarks}</Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Document No.</Text>

      {/* Live Item Details */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Live Item Details</Text>

        <View style={styles.row}>
          <Picker
            selectedValue={conversionType}
            onValueChange={(itemValue) => setConversionType(itemValue)}
            style={styles.picker}
          >
            <Picker.Item label="Conversion Type" value="" />
            <Picker.Item label="Type 1" value="type1" />
          </Picker>

          <TouchableOpacity style={styles.qtyButton}><Text>Stock Qty</Text></TouchableOpacity>
          <TextInput placeholder="Nos" style={styles.inputBox} />
        </View>

        <View style={styles.row}>
          <Picker
            selectedValue={liveItem}
            onValueChange={(itemValue) => setLiveItem(itemValue)}
            style={styles.picker}
          >
            <Picker.Item label="Live Item" value="" />
            <Picker.Item label="Item 1" value="item1" />
          </Picker>

          <TextInput placeholder="Wt" style={styles.inputBox} />
          <Picker
            selectedValue={port}
            onValueChange={(itemValue) => setPort(itemValue)}
            style={styles.picker}
          >
            <Picker.Item label="Select Port" value="" />
            <Picker.Item label="Port A" value="portA" />
          </Picker>
        </View>

        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.addButton}><Text style={styles.buttonText}>Add</Text></TouchableOpacity>
          <TouchableOpacity style={styles.clearButton}><Text style={styles.buttonText}>Clear</Text></TouchableOpacity>
        </View>

        <View style={styles.tableHeader}>
          <Text style={styles.headerCell}>LineNumber</Text>
          <Text style={styles.headerCell}>Item Name</Text>
          <Text style={styles.headerCell}>Nos</Text>
          <Text style={styles.headerCell}>Kgs</Text>
          <Text style={styles.headerCell}>Remarks</Text>
        </View>

        <FlatList
          data={liveItemsList}
          renderItem={renderItemRow}
          keyExtractor={(item, index) => index.toString()}
        />

        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.deleteButton}><Text style={styles.buttonText}>Delete</Text></TouchableOpacity>
          <TouchableOpacity style={styles.footerButton}><Text>Wt(Kg)</Text></TouchableOpacity>
          <TouchableOpacity style={styles.footerButton}><Text>Prod Value</Text></TouchableOpacity>
        </View>
      </View>

      {/* Dress Item Details */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Dress Item Details</Text>

        <Picker
          selectedValue={dressItem}
          onValueChange={(itemValue) => setDressItem(itemValue)}
          style={styles.pickerFull}
        >
          <Picker.Item label="Select Dress Item" value="" />
          <Picker.Item label="Dress 1" value="dress1" />
        </Picker>

        <View style={styles.row}>
          <TextInput placeholder="Nos" style={styles.inputBox} />
          <TextInput placeholder="Wt" style={styles.inputBox} />
          <Picker
            selectedValue={port}
            onValueChange={(itemValue) => setPort(itemValue)}
            style={styles.picker}
          >
            <Picker.Item label="Select Port" value="" />
            <Picker.Item label="Port A" value="portA" />
          </Picker>
        </View>

        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.addButton}><Text style={styles.buttonText}>Add</Text></TouchableOpacity>
          <TouchableOpacity style={styles.clearButton}><Text style={styles.buttonText}>Clear</Text></TouchableOpacity>
        </View>

        <View style={styles.tableHeader}>
          <Text style={styles.headerCell}>LineNumber</Text>
          <Text style={styles.headerCell}>Item Name</Text>
          <Text style={styles.headerCell}>Nos</Text>
          <Text style={styles.headerCell}>Kgs</Text>
          <Text style={styles.headerCell}>Remarks</Text>
        </View>

        <FlatList
          data={dressItemsList}
          renderItem={renderItemRow}
          keyExtractor={(item, index) => index.toString()}
        />

        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.deleteButton}><Text style={styles.buttonText}>Delete</Text></TouchableOpacity>
          <TouchableOpacity style={styles.footerButton}><Text>Total Pcs</Text></TouchableOpacity>
          <TouchableOpacity style={styles.footerButton}><Text>Total Wt</Text></TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: { padding: 10, backgroundColor: '#fff' },
  header: { fontSize: 18, fontWeight: 'bold', textAlign: 'center', marginVertical: 10, borderWidth: 1, borderRadius: 5, padding: 5 },
  section: { marginVertical: 10, padding: 10, borderRadius: 10, backgroundColor: '#eee' },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', marginBottom: 5 },
  row: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
  picker: { flex: 1, height: 40, borderWidth: 1, borderRadius: 5, marginHorizontal: 5 },
  pickerFull: { width: '100%', height: 40, borderWidth: 1, borderRadius: 5, marginBottom: 10 },
  inputBox: { flex: 1, height: 40, borderWidth: 1, borderRadius: 5, paddingHorizontal: 5, marginHorizontal: 5 },
  qtyButton: { backgroundColor: '#FDE68A', padding: 10, borderRadius: 5, marginHorizontal: 5 },
  buttonRow: { flexDirection: 'row', justifyContent: 'space-evenly', marginVertical: 10 },
  addButton: { backgroundColor: 'green', paddingVertical: 10, paddingHorizontal: 20, borderRadius: 5 },
  clearButton: { backgroundColor: 'red', paddingVertical: 10, paddingHorizontal: 20, borderRadius: 5 },
  deleteButton: { backgroundColor: 'red', padding: 10, borderRadius: 5 },
  footerButton: { backgroundColor: '#ccc', padding: 10, borderRadius: 5, marginHorizontal: 5 },
  buttonText: { color: 'white', fontWeight: 'bold' },
  tableHeader: { flexDirection: 'row', backgroundColor: '#ddd', paddingVertical: 5 },
  headerCell: { flex: 1, fontWeight: 'bold', textAlign: 'center' },
  tableRow: { flexDirection: 'row', paddingVertical: 5, borderBottomWidth: 1, borderColor: '#ccc' },
  cell: { flex: 1, textAlign: 'center' },
});

export default LiveToDress;
