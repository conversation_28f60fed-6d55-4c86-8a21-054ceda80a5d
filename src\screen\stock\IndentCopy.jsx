import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    StyleSheet,
    Modal,
    TouchableOpacity,
    ActivityIndicator,
    FlatList,
    ScrollView
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

const Indent = () => {
    const navigation = useNavigation();
    const [selectedTransferType, setSelectedTransferType] = useState(null);
    const [itemName, setItemName] = useState('');
    const [nos, setNos] = useState('');
    const [kgs, setKgs] = useState('');
    const [remarks, setRemarks] = useState('');
    const [itemList, setItemList] = useState([]);
    const [selectedItems, setSelectedItems] = useState({});
    const [locationModalVisible, setLocationModalVisible] = useState(false);
    const [branches, setBranches] = useState([]);
    const [filteredBranches, setFilteredBranches] = useState([]);
    const [branchSearch, setBranchSearch] = useState('');
    const [selectedBranchName, setSelectedBranchName] = useState('');
    const [modalVisible, setModalVisible] = useState(false);
    const [locations, setLocations] = useState([]); // Placeholder
    const [loading, setLoading] = useState(false);

    const [itemModalVisible, setItemModalVisible] = useState(false);
    const [items, setItems] = useState([]);
    const [filteredItems, setFilteredItems] = useState([]);
    const [itemSearch, setItemSearch] = useState('');
    const [selectedItemDetails, setSelectedItemDetails] = useState(null);
    const [loadingItems, setLoadingItems] = useState(false);


    const fetchItems = async () => {
        try {
            setLoadingItems(true);

            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            if (!token || !selectedBranch) {
                alert('Missing token or branch information');
                return;
            }

            const branchId = JSON.parse(selectedBranch).BranchId;

            const response = await axios.get(
                `https://retailuat.abisibg.com/api/v1/itemlist?branchId=${branchId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            console.log('Item API data:', response.data); // Debug log

            const data = response.data;
            if (Array.isArray(data) && data.length > 0) {
                setItems(data);
                setFilteredItems(data);
            } else {
                alert('No items received from API');
            }

        } catch (error) {
            alert('Error fetching items');
            console.error('FetchItems Error:', error?.response?.data || error.message);
        } finally {
            setLoadingItems(false);
        }
    };


    useEffect(() => {
        if (itemSearch.trim() === '') {
            setFilteredItems(items);
        } else {
            const filtered = items.filter(item =>
                item.ItemName?.toLowerCase().includes(itemSearch.toLowerCase())
            );
            setFilteredItems(filtered);
        }
    }, [itemSearch, items]);

    // Open item modal & fetch items
    const openItemModal = () => {
        setSelectedItemDetails(null);
        setItemSearch('');
        fetchItems();
        setItemModalVisible(true);
    };



    // Handle select item from modal list
    const handleSelectItem = (item) => {
        setSelectedItemDetails(item);
        setItemName(item.ItemName || ''); // Fill in itemName input
        setItemModalVisible(false);
    };

    // Close modal and reset selected item details
    const closeItemModal = () => {
        setItemModalVisible(false);
        setSelectedItemDetails(null);
    };


    const fetchBranches = async () => {
        try {
            setLoading(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const userDataString = await AsyncStorage.getItem('userData');
            const userData = JSON.parse(userDataString);
            const employeeId = userData?.userId;

            const response = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchaccess?EmpId=${employeeId}`,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const branches = response.data;
            if (Array.isArray(branches) && branches.length > 0) {
                const branchNames = branches.map(item => item.BranchName);
                setBranches(branchNames);
                setFilteredBranches(branchNames);
            } else {
                alert('No branches found.');
            }
        } catch (error) {
            console.error('Fetch branches error:', error?.response?.data || error.message);
            alert('Error fetching branches');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchBranches();
    }, []);

    useEffect(() => {
        const filtered = branchSearch.trim() === ''
            ? branches
            : branches.filter(branch =>
                branch.toLowerCase().includes(branchSearch.toLowerCase())
            );
        setFilteredBranches(filtered);
    }, [branchSearch, branches]);

    const handleSelectBranch = (branch) => {
        setSelectedBranchName(branch);
        setModalVisible(false);
        setBranchSearch('');
        // setFilteredBranches([]);
    };


    const handleAddItem = () => {
        if (itemName && nos && kgs) {
            const newItem = {
                lineNumber: itemList.length + 1,
                itemName,
                nos,
                kgs,
                remarks,
                key: `${itemList.length + 1}`,
            };
            setItemList([...itemList, newItem]);
            setItemName('');
            setNos('');
            setKgs('');
            setRemarks('');
        }
    };

    const handleDeleteSelected = () => {
        const newList = itemList.filter(item => !selectedItems[item.key]);
        const updatedList = newList.map((item, index) => ({
            ...item,
            lineNumber: index + 1,
            key: `${index + 1}`,
        }));
        setItemList(updatedList);
        setSelectedItems({});
    };

    const toggleCheckbox = (key) => {
        setSelectedItems((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    return (
        <View style={styles.container}>
            <Navbar />
            <View style={styles.headerRow}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-left" size={20} color="#000" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>INDENT</Text>
                <View style={styles.actionButtons}>
                    <TouchableOpacity style={styles.newBtn}>
                        <Icon name="user-plus" size={16} color="#000" />
                        <Text style={styles.actionText}> New</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.viewBtn}>
                        <Icon name="eye" size={16} color="#000" />
                        <Text style={styles.actionText}> View</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.saveBtn}>
                        <Text style={styles.saveText}>Save</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.cancelBtn}>
                        <Text style={styles.cancelText}>Cancel</Text>
                    </TouchableOpacity>
                </View>
            </View>


            {/* Location Section */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Location Details</Text>
                <View style={styles.row}>
                    <Dropdown
                        data={[
                            { label: 'HO', value: 'Branch Transfer' },
                            { label: 'IB ST', value: 'Supplier Return' },
                            { label: 'LOCAL VENDOR', value: 'Direct Purchase' },
                        ]}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Indent Type"
                        value={selectedTransferType}
                        onChange={(item) => setSelectedTransferType(item.value)}
                        style={styles.indentDetails_dropdown}
                    />

                    <View style={styles.rightInputContainer}>
                        <TextInput
                            placeholder="Choose Location"
                            value={selectedBranchName}
                            editable={false}
                            style={styles.input}
                        />
                        <TouchableOpacity
                            onPress={() => {
                                setFilteredBranches(branches); // restore full list
                                setModalVisible(true);
                            }}
                            style={[styles.selectBtn, { height: 50 }]}
                        >
                            <Text style={styles.selectBtnText}>Select</Text>
                        </TouchableOpacity>
                    </View>

                </View>

                <Modal visible={modalVisible} animationType="slide" transparent={true}>
                    <View style={styles.modalOverlay}>
                        <View style={styles.modalContent}>
                            <Text style={styles.modalTitle}>Choose Location</Text>

                            <TextInput
                                placeholder="Search Branch"
                                value={branchSearch}
                                onChangeText={setBranchSearch}
                                style={styles.ModalSearchinput}
                            />

                            <ScrollView style={{ marginVertical: 10 }}>
                                {filteredBranches.length > 0 ? (
                                    <View style={styles.branchGrid}>
                                        {filteredBranches.map((branch, index) => (
                                            <TouchableOpacity
                                                key={index}
                                                style={styles.branchCard}
                                                onPress={() => handleSelectBranch(branch)}
                                            >
                                                <Text style={styles.branchText}>{branch}</Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                ) : (
                                    <Text style={styles.noBranchText}>No location found</Text>
                                )}
                            </ScrollView>

                            <TouchableOpacity onPress={() => setModalVisible(false)} style={styles.closeModalBtn}>
                                <Text style={styles.closeModalText}>Close</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>


            </View>

            {/* Item Details Section */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Item Details</Text>
                <View style={styles.itemRow}>
                    <TextInput
                        placeholder="Item Name"
                        style={styles.searchInputInline}
                        value={itemName}
                        onChangeText={setItemName}
                    />
                    <TouchableOpacity
                        style={[styles.searchBtn, { height: 50 }]}
                        onPress={openItemModal}
                    >
                        <Text style={styles.searchBtnText}>Search Item</Text>
                    </TouchableOpacity>
                </View>
                {/* Item Search Modal */}
                <Modal visible={itemModalVisible} animationType="slide" transparent={true}>
                    <View style={styles.modalOverlay}>
                        <View style={styles.modalContent}>
                            <Text style={styles.modalTitle}>Search Items</Text>

                            <TextInput
                                placeholder="Search item by name"
                                value={itemSearch}
                                onChangeText={setItemSearch}
                                style={styles.ModalSearchinput}
                            />

                            {loadingItems ? (
                                <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                            ) : (
                                <>
                                    {!selectedItemDetails ? (
                                        <FlatList
                                            data={filteredItems}
                                            keyExtractor={(item) => item.ItemID.toString()}
                                            numColumns={4}
                                            columnWrapperStyle={styles.rowWrapper}
                                            style={{ marginTop: 10 }}
                                            renderItem={({ item }) => (
                                                <TouchableOpacity
                                                    style={styles.cardItem}
                                                    onPress={() => handleSelectItem(item)}
                                                >
                                                    <Text style={styles.cardItemText} numberOfLines={2} textAlign="center">
                                                        {item.ItemName}
                                                    </Text>
                                                </TouchableOpacity>
                                            )}
                                            ListEmptyComponent={
                                                <Text style={{ textAlign: 'center', marginTop: 20 }}>
                                                    No items found
                                                </Text>
                                            }
                                        />

                                    ) : (
                                        <ScrollView style={{ marginTop: 10 }}>
                                            <Text style={styles.modalTitle}>Item Details</Text>
                                            <Text><Text style={{ fontWeight: 'bold' }}>Item Code:</Text> {selectedItemDetails.ItemCode || 'N/A'}</Text>
                                            <Text><Text style={{ fontWeight: 'bold' }}>Item Name:</Text> {selectedItemDetails.ItemName}</Text>
                                            <Text><Text style={{ fontWeight: 'bold' }}>Description:</Text> {selectedItemDetails.Description || 'N/A'}</Text>
                                            <Text><Text style={{ fontWeight: 'bold' }}>Category:</Text> {selectedItemDetails.Category || 'N/A'}</Text>
                                            {/* Add any other fields from the item object here */}

                                            <TouchableOpacity
                                                style={[styles.backBtn, { marginTop: 20 }]}
                                                onPress={() => setSelectedItemDetails(null)}
                                            >
                                                <Text style={styles.btnText}>Back to List</Text>
                                            </TouchableOpacity>
                                        </ScrollView>
                                    )}
                                </>
                            )}

                            <TouchableOpacity
                                onPress={closeItemModal}
                                style={styles.closeModalBtn}
                            >
                                <Text style={styles.closeModalText}>Close</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
                <View style={styles.row}>
                    <TextInput placeholder="Wt(Kg)" style={styles.inputSmall} keyboardType="numeric" value={kgs} onChangeText={setKgs} />
                    <TextInput placeholder="Rate" style={styles.inputSmall} />
                    <TextInput placeholder="Nos" style={styles.inputSmall} keyboardType="numeric" value={nos} onChangeText={setNos} />
                </View>
                <TextInput
                    placeholder="Add Remarks"
                    style={{
                        height: 70,
                        borderColor: '#ccc',
                        backgroundColor: 'white',
                        borderWidth: 1,
                        borderRadius: 8,
                        padding: 10,
                        textAlignVertical: 'top', // Important for Android to align text at top
                    }}
                    multiline={true}
                    numberOfLines={4}
                    value={remarks}
                    onChangeText={setRemarks}
                />
                <View style={styles.row}>
                    <TouchableOpacity style={styles.addBtn} onPress={handleAddItem}>
                        <Text style={styles.btnText}>Add</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.clearBtn}>
                        <Text style={styles.btnText}>Clear</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {/* </ScrollView> */}


            <View style={styles.tableContainer}>
                <ScrollView horizontal>
                    <View>
                        {/* Table Header */}
                        <View style={styles.tableHeader}>
                            <Text style={[styles.tableHeaderCell, { width: 100 }]}>Select</Text>
                            <Text style={[styles.tableHeaderCell, { width: 100 }]}>Line No.</Text>
                            <Text style={[styles.tableHeaderCell, { width: 180 }]}>Item</Text>
                            <Text style={[styles.tableHeaderCell, { width: 100 }]}>Nos</Text>
                            <Text style={[styles.tableHeaderCell, { width: 100 }]}>Kgs</Text>
                            <Text style={[styles.tableHeaderCell, { width: 190 }]}>Remarks</Text>
                        </View>

                        {/* Table Rows - FlatList inside a view with fixed max height */}
                        <View style={{ maxHeight: 390 }}>
                            <FlatList
                                data={itemList}
                                keyExtractor={(item) => item.key}
                                renderItem={({ item }) => (
                                    <View style={styles.tableRow}>

                                        <View
                                            style={[
                                                styles.tableCell,
                                                { width: 100, },
                                                selectedItems[item.key] && { Color: '#002b5c' },
                                            ]}
                                        >
                                            <CheckBox
                                                value={selectedItems[item.key] || false}
                                                onValueChange={() => toggleCheckbox(item.key)}
                                                tintColors={{
                                                    true: '#002b5c',
                                                    false: '#002b5c',
                                                }}
                                            />
                                        </View>


                                        <Text style={[styles.tableCell, { width: 100 }]}>{item.lineNumber}</Text>
                                        <Text style={[styles.tableCell, { width: 180 }]}>{item.itemName}</Text>
                                        <Text style={[styles.tableCell, { width: 100 }]}>{item.nos}</Text>
                                        <Text style={[styles.tableCell, { width: 100 }]}>{item.kgs}</Text>
                                        <Text style={[styles.tableCell, { width: 190 }]}>{item.remarks}</Text>
                                    </View>
                                )}
                            />
                        </View>
                    </View>
                </ScrollView>

                {/* Delete Button - Outside FlatList, fixed at bottom of table */}
                <TouchableOpacity style={styles.deleteBtn} onPress={handleDeleteSelected}>
                    <Text style={styles.deleteBtnText}>Delete Selected Row</Text>
                </TouchableOpacity>
            </View>


        </View>
    );
};

const styles = StyleSheet.create({

    cardItem: {
        backgroundColor: '#f9f9f9',
        padding: 16,
        marginVertical: 8,
        marginHorizontal: 10,
        borderRadius: 10,
        elevation: 3, // for Android
        shadowColor: '#000', // for iOS
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },

    cardItemText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },

    rowWrapper: {
        justifyContent: 'space-between',
        marginBottom: 10,
    },

    cardItem: {
        backgroundColor: '#FDC500',
        flex: 1,
        aspectRatio: 1, // Makes it square
        margin: 5,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 2,
    },

    cardItemText: {
        fontSize: 12,
        fontWeight: 'bold',
        textAlign: 'center',
        paddingHorizontal: 5,
    },


















    modalOverlay: { flex: 1, justifyContent: 'center', backgroundColor: 'rgba(0,0,0,0.5)' },
    modalContent: { margin: 20, backgroundColor: '#fff', height: '900', padding: 20, borderRadius: 10 },
    locationName: { padding: 10, fontSize: 16 },
    closeModalText: { color: 'white', padding: 6, fontSize: 14, fontWeight: 'bold', },
    noBranchText: {
        textAlign: 'center',
        color: 'gray',
        marginTop: 20,
        fontSize: 16,
    },


    ModalSearchinput: {
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        borderRadius: 6,
        marginBottom: 10,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 10,
    },
    closeModalBtn: {
        backgroundColor: 'red',
        padding: 10,
        alignSelf: 'center',
        marginTop: 10,
        borderRadius: 8,

    },

    branchGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },

    branchCard: {
        width: '23%', // Slightly less than 25% to allow spacing
        aspectRatio: 1, // Makes it square
        marginBottom: 10,
        backgroundColor: '#f0f0f0',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FDC500',
        borderRadius: 8,
        elevation: 2,
        height: 23,
    },

    branchText: {
        fontSize: 14,
        textAlign: 'center',
    },






    container: {
        flex: 1,
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#EBEBEB',
        padding: 20,
    },

    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },
    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    section: {
        backgroundColor: '#e9e9e9',
        marginTop: 10,
        borderRadius: 10,
        padding: 10,
    },
    sectionTitle: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 5,
    },
    row: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: 10,
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    input: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 5,
        height: 50,
    },

    itemRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    itemInput: {
        flex: 1,
        width: '100%',
        borderWidth: 1,
        borderColor: '#ccc',
        height: 50,
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 10,
    },
    inputSmall: {
        height: 50,
        width: '32.5%',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
    },
    rightInputContainer: {
        flexDirection: 'row',
        flex: 1,
        alignItems: 'center',
        marginLeft: 10,

    },
    selectBtn: {
        backgroundColor: '#002b5c',
        paddingHorizontal: 16,
        borderRadius: 6,
        marginLeft: 5,
        justifyContent: 'center',
    },
    selectBtnText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    searchInputInline: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 10,
        height: 50,
    },
    searchBtn: {
        backgroundColor: '#002b5c',
        paddingHorizontal: 16,
        borderRadius: 6,
        justifyContent: 'center',
    },
    searchBtnText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    addBtn: {
        backgroundColor: 'green',
        padding: 14,
        borderRadius: 6,
        flex: 1,
        marginRight: 5,
        marginTop: 15,
        alignItems: 'center',
    },
    clearBtn: {
        marginTop: 15,
        backgroundColor: 'red',
        padding: 14,
        borderRadius: 6,
        flex: 1,
        marginLeft: 5,
        alignItems: 'center',
    },
    btnText: {
        color: '#fff',
        fontWeight: 'bold',
    },


    tableContainer: {
        marginTop: 10,
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 10,

    },

    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        paddingVertical: 12,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderColor: '#ccc',
    },
    tableHeaderText: {
        flex: 1,
        fontWeight: 'bold',
        // textAlign: 'center',
        color: 'white',
    },

    tableHeaderCell: {
        fontWeight: 'bold',
        // textAlign: 'center',
        color: 'white',
    },

    tableRow: {
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderColor: '#eee',
        alignItems: 'center',
    },

    tableCell: {
        paddingHorizontal: 5,
        // textAlign: 'center',
    },
    deleteBtn: {
        backgroundColor: 'red',
        padding: 10,
        marginTop: 10,
        borderRadius: 6,
        alignItems: 'center',
        marginBottom: 10,
    },
    deleteBtnText: {
        color: 'white',
        fontWeight: 'bold',
        paddingVertical: 5,
    },
    indentDetails_dropdown: {
        width: '50%',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        backgroundColor: 'white',
        marginBottom: 10,
    },


});

export default Indent;
