import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';
import { Dropdown } from 'react-native-element-dropdown';

const LiveToDress = () => {
    const navigation = useNavigation();

    return (
        <View style={styles.container}>
            <Navbar />
            <View style={styles.headerRow}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-left" size={20} color="#000" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Conversions</Text>
                <View style={styles.actionButtons}>
                    <TouchableOpacity style={styles.newBtn}>
                        <Icon name="user-plus" size={16} color="#000" />
                        <Text style={styles.actionText}> New</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.viewBtn}>
                        <Icon name="eye" size={16} color="#000" />
                        <Text style={styles.actionText}> View</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.saveBtn}>
                        <Text style={styles.saveText}>Save</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.cancelBtn}>
                        <Text style={styles.cancelText}>Cancel</Text>
                    </TouchableOpacity>
                </View>
            </View>
            <View style={styles.sectionBox}>
                <Text style={styles.sectionTitle}>Live Item Details</Text>

        <View style={styles.row}>
  <View style={styles.half}>
    <Dropdown
      data={[{ label: 'Type A', value: 'a' }, { label: 'Type B', value: 'b' }]}
      labelField="label"
      valueField="value"
      placeholder="Conversion Type"
      style={styles.dropdown}
      placeholderStyle={styles.placeholderStyle}
    />
  </View>
  <View style={styles.halfRight}>
    <TouchableOpacity style={styles.smallBtnYellow}>
      <Text style={styles.smallBtnText}>Stock Qty</Text>
    </TouchableOpacity>
    <TouchableOpacity style={styles.smallBtnGrey}>
      <Text style={styles.smallBtnText}>Nos</Text>
    </TouchableOpacity>
  </View>
</View>

<View style={styles.row}>
  <View style={styles.half}>
    <Dropdown
      data={[{ label: 'Item 1', value: 'item1' }, { label: 'Item 2', value: 'item2' }]}
      labelField="label"
      valueField="value"
      placeholder="Live Item"
      style={styles.dropdown}
      placeholderStyle={styles.placeholderStyle}
    />
  </View>
  <View style={styles.halfRight}>
    <TouchableOpacity style={styles.smallBtnGrey}>
      <Text style={styles.smallBtnText}>Wt</Text>
    </TouchableOpacity>
    <Dropdown
      data={[{ label: 'Port A', value: 'portA' }, { label: 'Port B', value: 'portB' }]}
      labelField="label"
      valueField="value"
      placeholder="Select Port"
      style={styles.smallDropdown}
      placeholderStyle={styles.placeholderStyle}
    />
  </View>
</View>

<View style={styles.row}>
  <TouchableOpacity style={styles.addBtn}>
    <Text style={styles.addText}>Add</Text>
  </TouchableOpacity>
  <TouchableOpacity style={styles.clearBtn}>
    <Text style={styles.clearText}>Clear</Text>
  </TouchableOpacity>
</View>


            </View>

        </View>

    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#EBEBEB',
        padding: 20,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 10,
        paddingHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
        marginLeft: 5,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 10,
        paddingHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
        marginLeft: 5,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
        marginLeft: 5,
    },
    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
        marginLeft: 5,
    },
    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },


    // Live to dress css 
    sectionBox: {
        margin: 10,
        padding: 5,
        backgroundColor: '#F6F6F6',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: '#DADADA',
    },
    sectionTitle: {
        fontWeight: 'bold',
        marginBottom: 10,
        marginLeft:10,
        fontSize: 16,
    },


    row: {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: 10,
  paddingHorizontal: 10,
},

half: {
  width: '50%',
  paddingRight: 5,
},

halfRight: {
  width: '50%',
  flexDirection: 'row',
  justifyContent: 'space-between',
  gap: 5,
},

dropdown: {
  height: 40,
  borderWidth: 1,
  borderColor: '#ccc',
  borderRadius: 8,
  paddingHorizontal: 10,
},

smallDropdown: {
  width: 182,
  height: 40,
  borderWidth: 1,
  borderColor: '#ccc',
  borderRadius: 8,
  paddingHorizontal: 10,
},

placeholderStyle: {
  color: '#999',
},

smallBtnYellow: {
  backgroundColor: '#FAD961',
  paddingVertical: 10,
  paddingHorizontal: 15,
   width: 182,
  borderRadius: 8,
  justifyContent: 'center',
  alignItems: 'center',
},

smallBtnGrey: {
  backgroundColor: '#E2E3E5',
  paddingVertical: 10,
  width: 182,
  paddingHorizontal: 15,
  borderRadius: 8,
  justifyContent: 'center',
  alignItems: 'center',
},

smallBtnText: {
  fontSize: 14,
  fontWeight: 'bold',
},

addBtn: {
  backgroundColor: 'green',
  flex: 1,
  paddingVertical: 12,
  marginRight: 5,
  borderRadius: 8,
  alignItems: 'center',
},

clearBtn: {
  backgroundColor: 'red',
  flex: 1,
  paddingVertical: 12,
  marginLeft: 5,
  borderRadius: 8,
  alignItems: 'center',
},

addText: {
  color: '#fff',
  fontSize: 16,
  fontWeight: 'bold',
},

clearText: {
  color: '#fff',
  fontSize: 16,
  fontWeight: 'bold',
},





    tableHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: '#ccc',
        paddingVertical: 10,
        paddingHorizontal: 5,
        borderRadius: 5,
    },
    tableCell: {
        flex: 1,
        fontWeight: 'bold',
        fontSize: 12,
        textAlign: 'center',
    },
    deleteBtn: {
        backgroundColor: 'red',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        marginRight: 10,
    },
    deleteText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    prodBtn: {
        backgroundColor: '#ccc',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        marginRight: 10,
    },
    prodText: {
        fontWeight: 'bold',
    },

});

export default LiveToDress;
